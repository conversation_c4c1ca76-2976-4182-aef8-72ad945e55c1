import { useState, useMemo, useEffect } from "react";
import { Link, useLocation } from "react-router-dom";
import { Hero, Container, Loading } from "@/ui";
import { MapPin, Calendar } from "lucide-react";
import { IMAGE_PATHS } from "@/lib/config/images";
import { useData, useSeasonalData } from "@/lib/hooks";
import { getProjects, getServices } from "@/lib/api";
import { ImageService } from "@/lib/services/ImageService";
import {
  filterProjects,
  getProjectsForService,
  getServicesWithProjects,
  getUniqueProjectLocations
} from "@/lib/utils/filtering";
import { logSeasonalAccess } from "@/lib/utils/debug";
import { encodeImagePath } from "@/lib/utils/paths";
import { getPageSeo } from "@/lib/constants/seo";
import { Meta } from "@/layout/Meta";

const ProjectsPage = () => {
    const location = useLocation();
    const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
    const [selectedLocation, setSelectedLocation] = useState<string | null>(null);

    // Read URL parameters on component mount
    useEffect(() => {
        const searchParams = new URLSearchParams(location.search);
        const category = searchParams.get('category');
        const locationParam = searchParams.get('location');

        if (category) {
            setSelectedCategory(category);
        }
        if (locationParam) {
            setSelectedLocation(locationParam);
        }
    }, [location.search]);

    // Use our new seasonal hook to get seasonal data
    const { currentSeason } = useSeasonalData();

    // Log seasonal access for debugging
    useEffect(() => {
        logSeasonalAccess('ProjectsPage', currentSeason, {
            component: 'ProjectsPage'
        });
    }, [currentSeason]);

    // Use the useData hook to fetch projects and services
    const { data: projects, loading: projectsLoading } = useData(getProjects, []);
    const { data: services, loading: servicesLoading } = useData(getServices, []);

    // Use empty arrays as fallbacks when data is undefined
    const projectsData = projects || [];
    const servicesData = services || [];

    // Get unique locations from projects, filtered by selected category if applicable
    const locations = useMemo(() => {
        // Use the centralized utility function to get unique locations
        return getUniqueProjectLocations(projectsData, selectedCategory, servicesData);
    }, [selectedCategory, projectsData, servicesData]);

    // Get service IDs that have associated projects based on current filters
    const serviceIdsWithProjects = useMemo(() => {
        // Use the centralized utility function to get services with projects
        const servicesWithProjects = getServicesWithProjects(servicesData, projectsData, selectedLocation);

        // Return just the IDs
        return servicesWithProjects.map(service => service.id);
    }, [selectedLocation, projectsData, servicesData]); // Re-calculate when location changes

    // Reset location if it's not in the filtered locations list
    useEffect(() => {
        if (selectedLocation && !locations.includes(selectedLocation)) {
            setSelectedLocation(null);
        }
    }, [locations, selectedLocation]);

    // Filter projects based on selected filters
    const filteredProjects = useMemo(() => {
        if (selectedCategory) {
            // Use the specialized utility for service-project relationship
            return getProjectsForService(projectsData, selectedCategory, servicesData, selectedLocation);
        } else {
            // Use the general filtering utility when no category is selected
            return filterProjects(projectsData, null, selectedLocation);
        }
    }, [selectedCategory, selectedLocation, projectsData, servicesData]);

    // Reset all filters
    const resetFilters = () => {
        setSelectedCategory(null);
        setSelectedLocation(null);
    };

    // Show loading state while data is being fetched
    if (projectsLoading || servicesLoading) {
        return <Loading message="Laster prosjekter..." fullScreen />;
    }

    // Get centralized SEO data for projects page
    const seoData = getPageSeo('projects');

    return (
        <>
            <Meta
                title={seoData.title}
                description={seoData.description}
                keywords={seoData.keywords}
                schema={seoData.schema}
            />
            <div>
                <Hero
                title="Våre prosjekter"
                subtitle="Utforsk våre tidligere prosjekter og la deg inspirere til ditt neste uterom"
                actionText=""
                backgroundImage={ImageService.getHeroImage('projects-showcase')}
            />

            <Container className="py-12">
                {/* Filters */}
                <div className="mb-8 p-6 bg-gray-50 rounded-lg">
                    <h2 className="text-xl font-semibold mb-4">
                        Filtrer prosjekter
                    </h2>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        {/* Category filter */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                Kategori
                            </label>
                            <select
                                value={selectedCategory || ""}
                                onChange={(e) => {
                                    const newCategory = e.target.value || null;
                                    setSelectedCategory(newCategory);
                                    // Reset location when category changes to avoid inconsistent filtering
                                    setSelectedLocation(null);
                                }}
                                className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
                                disabled={serviceIdsWithProjects.length === 0}
                            >
                                <option value="">
                                    {serviceIdsWithProjects.length === 0 ? "Ingen kategorier tilgjengelig" : "Alle kategorier"}
                                </option>
                                {servicesData
                                    .filter(service => serviceIdsWithProjects.includes(service.id))
                                    .map((service) => (
                                        <option key={service.id} value={service.id}>
                                            {service.title}
                                        </option>
                                    ))}
                            </select>
                        </div>

                        {/* Location filter */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                Sted
                            </label>
                            <select
                                value={selectedLocation || ""}
                                onChange={(e) =>
                                    setSelectedLocation(e.target.value || null)
                                }
                                className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
                                disabled={locations.length === 0}
                            >
                                <option value="">
                                    {locations.length === 0 ? "Ingen steder tilgjengelig" : "Alle steder"}
                                </option>
                                {locations.map((location) => (
                                    <option key={location} value={location}>
                                        {location}
                                    </option>
                                ))}
                            </select>
                        </div>

                        {/* Reset button */}
                        <div className="flex items-end">
                            <button
                                onClick={resetFilters}
                                className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors"
                            >
                                Nullstill filtre
                            </button>
                        </div>
                    </div>
                </div>

                {/* Projects grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {filteredProjects.map((project) => (
                        <Link
                            key={project.id}
                            to={`/prosjekter/${project.id}`}
                            className="group block overflow-hidden rounded-lg shadow-md hover:shadow-xl transition-shadow"
                        >
                            <div className="relative h-64 overflow-hidden">
                                <img
                                    src={encodeImagePath(project.image)}
                                    alt={project.title}
                                    className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                                    loading="lazy"
                                />
                                <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent opacity-80 group-hover:opacity-90 transition-opacity" />
                            </div>

                            <div className="p-4">
                                <h3 className="text-xl font-semibold mb-2 group-hover:text-green-600 transition-colors">
                                    {project.title}
                                </h3>

                                <p className="text-gray-600 mb-4 line-clamp-2">
                                    {project.description}
                                </p>

                                <div className="flex flex-wrap items-center gap-4 text-sm text-gray-500">
                                    <div className="flex items-center gap-1">
                                        <MapPin className="w-4 h-4" />
                                        <span>{project.location}</span>
                                    </div>
                                    <div className="flex items-center gap-1">
                                        <Calendar className="w-4 h-4" />
                                        <span>{project.completionDate}</span>
                                    </div>
                                </div>

                                <div className="mt-3 pt-3 border-t border-gray-100">
                                    <span className="inline-block px-2 py-1 bg-green-100 text-green-800 text-xs rounded">
                                        {servicesData.find(
                                            (s) =>
                                                s.id ===
                                                project.category.toLowerCase()
                                        )?.title || project.category}
                                    </span>
                                </div>
                            </div>
                        </Link>
                    ))}
                </div>

                {filteredProjects.length === 0 && (
                    <div className="text-center py-12">
                        <h3 className="text-xl font-semibold mb-2">
                            Ingen prosjekter funnet
                        </h3>
                        <p className="text-gray-600">
                            Prøv å endre filtrene eller{" "}
                            <button
                                onClick={resetFilters}
                                className="text-green-600 hover:underline"
                            >
                                vis alle prosjekter
                            </button>
                        </p>
                    </div>
                )}
            </Container>
            </div>
        </>
    );
};

export default ProjectsPage;
