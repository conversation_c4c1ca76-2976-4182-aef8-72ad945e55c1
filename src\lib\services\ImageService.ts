/**
 * ImageService - Type-safe dynamic image loading service
 * 
 * This service provides a clean API for accessing dynamically loaded images
 * while preserving all existing functionality and mapping relationships.
 * It maintains backward compatibility with the existing service-to-category
 * and project-to-category mapping systems.
 */

import { imageCollections } from '@/lib/assets/imageLoader';
import { encodeImagePath } from '@/lib/utils/paths';
import { GeoImage, extractGeoCoordinates, IMAGE_CATEGORIES } from '@/lib/utils/images';

// Import existing mapping configurations to preserve relationships
import {
  SERVICE_TO_IMAGE_CATEGORY,
  PROJECT_CATEGORY_TO_IMAGE_CATEGORY,
  FEATURED_IMAGES
} from '@/lib/config/images';

/**
 * Main ImageService class providing type-safe image access
 */
export class ImageService {
  /**
   * Get hero image by key
   * @param key - Hero image key (e.g., 'home-main', 'about-ringerike')
   * @returns Encoded image URL with fallback
   */
  static getHeroImage(key: string): string {
    const image = imageCollections.hero[key];
    if (image) {
      return encodeImagePath(image);
    }
    
    console.warn(`Hero image not found: ${key}. Available keys:`, Object.keys(imageCollections.hero));
    return this.getFallbackHero();
  }

  /**
   * Get team member image by ID
   * @param memberId - Team member ID (e.g., 'kim', 'jan', 'firma')
   * @returns Encoded image URL with fallback
   */
  static getTeamImage(memberId: string): string {
    const image = imageCollections.team[memberId];
    if (image) {
      return encodeImagePath(image);
    }
    
    console.warn(`Team image not found: ${memberId}. Available keys:`, Object.keys(imageCollections.team));
    return this.getFallbackTeam();
  }

  /**
   * Get featured image for a category
   * @param category - Image category (e.g., 'belegg', 'stål', 'støttemur')
   * @returns Promise resolving to encoded image URL with fallback
   */
  static async getCategoryFeatured(category: string): Promise<string> {
    const categoryLoader = imageCollections.categories[category];
    if (categoryLoader) {
      try {
        const images = await categoryLoader();
        if (images.length > 0) {
          // Use first image as featured (maintains current behavior)
          return encodeImagePath(images[0]);
        }
      } catch (error) {
        console.error(`Error loading category images for ${category}:`, error);
      }
    }
    
    console.warn(`Category not found: ${category}. Available categories:`, Object.keys(imageCollections.categories));
    return this.getFallbackCategory();
  }

  /**
   * Get service image using existing service-to-category mapping
   * @param serviceId - Service ID (e.g., 'belegningsstein', 'cortenstaal')
   * @returns Promise resolving to encoded image URL
   */
  static async getServiceImage(serviceId: string): Promise<string> {
    const category = SERVICE_TO_IMAGE_CATEGORY[serviceId as keyof typeof SERVICE_TO_IMAGE_CATEGORY];
    if (category) {
      return this.getCategoryFeatured(category);
    }
    
    console.warn(`Service ID not found in mapping: ${serviceId}. Available service IDs:`, Object.keys(SERVICE_TO_IMAGE_CATEGORY));
    return this.getFallbackCategory();
  }

  /**
   * Get project image using existing project-to-category mapping
   * @param projectCategory - Project category (e.g., 'Cortenstål', 'Belegningsstein')
   * @returns Promise resolving to encoded image URL
   */
  static async getProjectImage(projectCategory: string): Promise<string> {
    const category = PROJECT_CATEGORY_TO_IMAGE_CATEGORY[projectCategory as keyof typeof PROJECT_CATEGORY_TO_IMAGE_CATEGORY];
    if (category) {
      return this.getCategoryFeatured(category);
    }
    
    console.warn(`Project category not found in mapping: ${projectCategory}. Available categories:`, Object.keys(PROJECT_CATEGORY_TO_IMAGE_CATEGORY));
    return this.getFallbackCategory();
  }

  /**
   * Get all images for a category (for galleries)
   * @param category - Image category
   * @returns Promise resolving to array of encoded image URLs
   */
  static async getCategoryImages(category: string): Promise<string[]> {
    const categoryLoader = imageCollections.categories[category];
    if (categoryLoader) {
      try {
        const images = await categoryLoader();
        return images.map(img => encodeImagePath(img));
      } catch (error) {
        console.error(`Error loading category images for ${category}:`, error);
      }
    }

    console.warn(`Category not found: ${category}. Available categories:`, Object.keys(imageCollections.categories));
    return [];
  }

  /**
   * Get all images for a category as GeoImage objects (for gallery components)
   * Maintains compatibility with existing gallery interfaces
   * @param category - Image category key
   * @returns Promise resolving to array of GeoImage objects
   */
  static async getCategoryGalleryImages(category: string): Promise<GeoImage[]> {
    const categoryLoader = imageCollections.categories[category];
    if (!categoryLoader) {
      console.warn(`Category not found: ${category}. Available categories:`, Object.keys(imageCollections.categories));
      return [];
    }

    try {
      const imageUrls = await categoryLoader();
      const categoryName = IMAGE_CATEGORIES[category as keyof typeof IMAGE_CATEGORIES] || category;

      return imageUrls.map(url => {
        // Extract filename from URL: /images/categorized/belegg/IMG_123.webp -> IMG_123.webp
        const filename = url.split('/').pop() || '';
        const { coordinates } = extractGeoCoordinates(filename);

        // Create GeoImage object compatible with existing interface
        const geoImage: GeoImage = {
          filename,
          path: url, // Use the full URL path
          category: categoryName,
          metadata: {
            title: `${categoryName} prosjekt`,
            description: `Profesjonell utførelse av ${categoryName.toLowerCase()}`
          }
        };

        // Add coordinates if available
        if (coordinates) {
          geoImage.coordinates = coordinates;
          if (geoImage.metadata) {
            geoImage.metadata.location = `${coordinates.latitude.toFixed(6)}, ${coordinates.longitude.toFixed(6)}`;
          }
        }

        return geoImage;
      });
    } catch (error) {
      console.error(`Error loading gallery images for ${category}:`, error);
      return [];
    }
  }

  /**
   * Get available categories
   * @returns Array of available category keys
   */
  static getAvailableCategories(): string[] {
    return Object.keys(imageCollections.categories);
  }

  /**
   * Check if a category exists
   * @param category - Category to check
   * @returns Boolean indicating if category exists
   */
  static hasCategory(category: string): boolean {
    return category in imageCollections.categories;
  }

  /**
   * Fallback hero image
   * @returns Default hero image path
   */
  static getFallbackHero(): string {
    return encodeImagePath('/images/hero/hero-home-main.webp');
  }

  /**
   * Fallback team image
   * @returns Default team image path
   */
  static getFallbackTeam(): string {
    return encodeImagePath('/images/team/ringerikelandskap-firma.webp');
  }

  /**
   * Fallback category image
   * @returns Default category image path
   */
  static getFallbackCategory(): string {
    return encodeImagePath('/images/hero/hero-services-granite.webp');
  }
}

/**
 * Synchronous versions for backward compatibility
 * These maintain the existing API while the codebase migrates
 *
 * Note: These use the existing FEATURED_IMAGES mapping to provide
 * immediate synchronous access during the migration period.
 */
export class ImageServiceSync {
  /**
   * Synchronous service image getter (for immediate migration)
   * Uses the existing featured images mapping for immediate access
   */
  static getServiceImageSync(serviceId: string): string {
    const category = SERVICE_TO_IMAGE_CATEGORY[serviceId as keyof typeof SERVICE_TO_IMAGE_CATEGORY];
    if (category) {
      // Use the imported FEATURED_IMAGES mapping for sync access
      const filename = FEATURED_IMAGES[category as keyof typeof FEATURED_IMAGES];
      if (filename) {
        const imagePath = `/images/categorized/${category}/${filename}`;
        return encodeImagePath(imagePath);
      }
    }

    console.warn(`Service image not found for: ${serviceId}. Available service IDs:`, Object.keys(SERVICE_TO_IMAGE_CATEGORY));
    return ImageService.getFallbackCategory();
  }

  /**
   * Synchronous project image getter (for immediate migration)
   * Uses the existing featured images mapping for immediate access
   */
  static getProjectImageSync(projectCategory: string): string {
    const category = PROJECT_CATEGORY_TO_IMAGE_CATEGORY[projectCategory as keyof typeof PROJECT_CATEGORY_TO_IMAGE_CATEGORY];
    if (category) {
      // Use the imported FEATURED_IMAGES mapping for sync access
      const filename = FEATURED_IMAGES[category as keyof typeof FEATURED_IMAGES];
      if (filename) {
        const imagePath = `/images/categorized/${category}/${filename}`;
        return encodeImagePath(imagePath);
      }
    }

    console.warn(`Project image not found for: ${projectCategory}. Available categories:`, Object.keys(PROJECT_CATEGORY_TO_IMAGE_CATEGORY));
    return ImageService.getFallbackCategory();
  }

  /**
   * Synchronous team image getter (for immediate migration)
   * Uses the dynamic image collections for immediate access
   */
  static getTeamImage(memberId: string): string {
    const image = imageCollections.team[memberId];
    if (image) {
      return encodeImagePath(image);
    }

    console.warn(`Team image not found: ${memberId}. Available keys:`, Object.keys(imageCollections.team));
    return ImageService.getFallbackTeam();
  }
}

export default ImageService;
