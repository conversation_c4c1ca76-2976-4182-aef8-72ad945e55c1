/**
 * ImageService - Type-safe dynamic image loading service
 * 
 * This service provides a clean API for accessing dynamically loaded images
 * while preserving all existing functionality and mapping relationships.
 * It maintains backward compatibility with the existing service-to-category
 * and project-to-category mapping systems.
 */

import { imageCollections } from '@/lib/assets/imageLoader';
import { encodeImagePath } from '@/lib/utils/paths';
import { GeoImage, extractGeoCoordinates, IMAGE_CATEGORIES } from '@/lib/utils/images';
import { ImageValidationService } from './ImageValidationService';
import { ImageCacheService } from './ImageCacheService';
import { ImagePerformanceService } from './ImagePerformanceService';

// Import existing mapping configurations to preserve relationships
import {
  SERVICE_TO_IMAGE_CATEGORY,
  PROJECT_CATEGORY_TO_IMAGE_CATEGORY,
  FEATURED_IMAGES
} from '@/lib/config/images';

/**
 * Main ImageService class providing type-safe image access
 */
export class ImageService {
  /**
   * Get hero image by key
   * @param key - Hero image key (e.g., 'home-main', 'about-ringerike')
   * @returns Encoded image URL with fallback
   */
  static getHeroImage(key: string): string {
    try {
      // Validate input
      if (!key || typeof key !== 'string') {
        console.error('Invalid hero image key:', key);
        return this.getFallbackHero();
      }

      const image = imageCollections.hero[key];
      if (image) {
        return encodeImagePath(image);
      }

      console.warn(`Hero image not found: ${key}. Available keys:`, Object.keys(imageCollections.hero));

      // Try to suggest similar keys
      const availableKeys = Object.keys(imageCollections.hero);
      const similarKey = availableKeys.find(k => k.includes(key) || key.includes(k));
      if (similarKey) {
        console.info(`Did you mean: ${similarKey}?`);
      }

      return this.getFallbackHero();
    } catch (error) {
      console.error('Error getting hero image:', error);
      return this.getFallbackHero();
    }
  }

  /**
   * Get team member image by ID
   * @param memberId - Team member ID (e.g., 'kim', 'jan', 'firma')
   * @returns Encoded image URL with fallback
   */
  static getTeamImage(memberId: string): string {
    const image = imageCollections.team[memberId];
    if (image) {
      return encodeImagePath(image);
    }
    
    console.warn(`Team image not found: ${memberId}. Available keys:`, Object.keys(imageCollections.team));
    return this.getFallbackTeam();
  }

  /**
   * Get featured image for a category
   * @param category - Image category (e.g., 'belegg', 'stål', 'støttemur')
   * @returns Promise resolving to encoded image URL with fallback
   */
  static async getCategoryFeatured(category: string): Promise<string> {
    const categoryLoader = imageCollections.categories[category];
    if (categoryLoader) {
      try {
        const images = await categoryLoader();
        if (images.length > 0) {
          // Use first image as featured (maintains current behavior)
          return encodeImagePath(images[0]);
        }
      } catch (error) {
        console.error(`Error loading category images for ${category}:`, error);
      }
    }
    
    console.warn(`Category not found: ${category}. Available categories:`, Object.keys(imageCollections.categories));
    return this.getFallbackCategory();
  }

  /**
   * Get service image using existing service-to-category mapping
   * @param serviceId - Service ID (e.g., 'belegningsstein', 'cortenstaal')
   * @returns Promise resolving to encoded image URL
   */
  static async getServiceImage(serviceId: string): Promise<string> {
    const category = SERVICE_TO_IMAGE_CATEGORY[serviceId as keyof typeof SERVICE_TO_IMAGE_CATEGORY];
    if (category) {
      return this.getCategoryFeatured(category);
    }
    
    console.warn(`Service ID not found in mapping: ${serviceId}. Available service IDs:`, Object.keys(SERVICE_TO_IMAGE_CATEGORY));
    return this.getFallbackCategory();
  }

  /**
   * Get project image using existing project-to-category mapping
   * @param projectCategory - Project category (e.g., 'Cortenstål', 'Belegningsstein')
   * @returns Promise resolving to encoded image URL
   */
  static async getProjectImage(projectCategory: string): Promise<string> {
    const category = PROJECT_CATEGORY_TO_IMAGE_CATEGORY[projectCategory as keyof typeof PROJECT_CATEGORY_TO_IMAGE_CATEGORY];
    if (category) {
      return this.getCategoryFeatured(category);
    }
    
    console.warn(`Project category not found in mapping: ${projectCategory}. Available categories:`, Object.keys(PROJECT_CATEGORY_TO_IMAGE_CATEGORY));
    return this.getFallbackCategory();
  }

  /**
   * Get all images for a category (for galleries)
   * @param category - Image category
   * @returns Promise resolving to array of encoded image URLs
   */
  static async getCategoryImages(category: string): Promise<string[]> {
    const categoryLoader = imageCollections.categories[category];
    if (categoryLoader) {
      try {
        const images = await categoryLoader();
        return images.map(img => encodeImagePath(img));
      } catch (error) {
        console.error(`Error loading category images for ${category}:`, error);
      }
    }

    console.warn(`Category not found: ${category}. Available categories:`, Object.keys(imageCollections.categories));
    return [];
  }

  /**
   * Get all images for a category as GeoImage objects (for gallery components)
   * Maintains compatibility with existing gallery interfaces
   * @param category - Image category key
   * @returns Promise resolving to array of GeoImage objects
   */
  static async getCategoryGalleryImages(category: string): Promise<GeoImage[]> {
    try {
      // Validate input
      if (!category || typeof category !== 'string') {
        console.error('Invalid category:', category);
        return [];
      }

      const categoryLoader = imageCollections.categories[category];
      if (!categoryLoader) {
        console.warn(`Category not found: ${category}. Available categories:`, Object.keys(imageCollections.categories));

        // Try to suggest similar categories
        const availableCategories = Object.keys(imageCollections.categories);
        const similarCategory = availableCategories.find(c =>
          c.includes(category.toLowerCase()) || category.toLowerCase().includes(c)
        );
        if (similarCategory) {
          console.info(`Did you mean: ${similarCategory}?`);
        }

        return [];
      }

      // Validate category before loading
      const validation = await ImageValidationService.validateCategory(category);
      if (!validation.isValid) {
        console.error(`Category validation failed for ${category}:`, validation.errors);
        // Continue anyway but log the issues
        validation.warnings.forEach(warning => console.warn(warning));
      }

      const imageUrls = await categoryLoader();

      if (!Array.isArray(imageUrls)) {
        console.error(`Invalid image data for category ${category}: expected array, got ${typeof imageUrls}`);
        return [];
      }

      if (imageUrls.length === 0) {
        console.warn(`No images found for category: ${category}`);
        return [];
      }

      const categoryName = IMAGE_CATEGORIES[category as keyof typeof IMAGE_CATEGORIES] || category;

      return imageUrls.map((url, index) => {
        try {
          // Validate URL
          if (!url || typeof url !== 'string') {
            console.error(`Invalid image URL at index ${index} in category ${category}:`, url);
            return null;
          }

          // Extract filename from URL: /images/categorized/belegg/IMG_123.webp -> IMG_123.webp
          const filename = url.split('/').pop() || '';
          const { coordinates } = extractGeoCoordinates(filename);

          // Create GeoImage object compatible with existing interface
          const geoImage: GeoImage = {
            filename,
            path: url, // Use the full URL path
            category: categoryName,
            metadata: {
              title: `${categoryName} prosjekt`,
              description: `Profesjonell utførelse av ${categoryName.toLowerCase()}`
            }
          };

          // Add coordinates if available
          if (coordinates) {
            geoImage.coordinates = coordinates;
            if (geoImage.metadata) {
              geoImage.metadata.location = `${coordinates.latitude.toFixed(6)}, ${coordinates.longitude.toFixed(6)}`;
            }
          }

          return geoImage;
        } catch (error) {
          console.error(`Error processing image at index ${index} in category ${category}:`, error);
          return null;
        }
      }).filter((image): image is GeoImage => image !== null); // Remove null entries

    } catch (error) {
      console.error(`Error loading gallery images for ${category}:`, error);

      // Log additional context for debugging
      console.error('Error context:', {
        category,
        availableCategories: Object.keys(imageCollections.categories),
        errorType: error instanceof Error ? error.constructor.name : typeof error,
        errorMessage: error instanceof Error ? error.message : String(error)
      });

      return [];
    }
  }

  /**
   * Get available categories
   * @returns Array of available category keys
   */
  static getAvailableCategories(): string[] {
    return Object.keys(imageCollections.categories);
  }

  /**
   * Check if a category exists
   * @param category - Category to check
   * @returns Boolean indicating if category exists
   */
  static hasCategory(category: string): boolean {
    return category in imageCollections.categories;
  }

  /**
   * Fallback hero image
   * @returns Default hero image path
   */
  static getFallbackHero(): string {
    try {
      // Try to get a working hero image first
      const availableHeroImages = Object.values(imageCollections.hero);
      if (availableHeroImages.length > 0) {
        return encodeImagePath(availableHeroImages[0]);
      }

      // Use hardcoded fallback
      return encodeImagePath(ImageValidationService.getFallbackImage('hero'));
    } catch (error) {
      console.error('Error getting fallback hero image:', error);
      return encodeImagePath('/images/hero/hero-home-main.webp');
    }
  }

  /**
   * Fallback team image with validation
   * @returns Default team image path
   */
  static getFallbackTeam(): string {
    try {
      // Try to get a working team image first
      const availableTeamImages = Object.values(imageCollections.team);
      if (availableTeamImages.length > 0) {
        return encodeImagePath(availableTeamImages[0]);
      }

      // Use hardcoded fallback
      return encodeImagePath(ImageValidationService.getFallbackImage('team'));
    } catch (error) {
      console.error('Error getting fallback team image:', error);
      return encodeImagePath('/images/team/ringerikelandskap-firma.webp');
    }
  }

  /**
   * Get icon image by key
   * @param key - Icon key (e.g., 'mittAnbud', 'google')
   * @returns Encoded icon URL with fallback
   */
  static getIconImage(key: string): string {
    try {
      // Validate input
      if (!key || typeof key !== 'string') {
        console.error('Invalid icon key:', key);
        return this.getFallbackIcon();
      }

      // Map icon keys to paths
      const iconPaths: Record<string, string> = {
        'mittAnbud': '/images/icons/mittanbud-icon.svg',
        'google': '/images/icons/google-icon.svg'
      };

      const iconPath = iconPaths[key];
      if (iconPath) {
        return encodeImagePath(iconPath);
      }

      console.warn(`Icon not found: ${key}. Available icons:`, Object.keys(iconPaths));
      return this.getFallbackIcon();
    } catch (error) {
      console.error('Error getting icon image:', error);
      return this.getFallbackIcon();
    }
  }

  /**
   * Fallback icon image
   * @returns Default icon path
   */
  static getFallbackIcon(): string {
    return encodeImagePath('/images/icons/mittanbud-icon.svg');
  }

  /**
   * Fallback category image with validation
   * @returns Default category image path
   */
  static getFallbackCategory(): string {
    try {
      // Use hardcoded fallback (async category loading not suitable for sync fallback)
      return encodeImagePath(ImageValidationService.getFallbackImage('category'));
    } catch (error) {
      console.error('Error getting fallback category image:', error);
      return encodeImagePath('/images/hero/hero-services-granite.webp');
    }
  }

  /**
   * Validate image collections on startup
   * @returns Validation result
   */
  static validateCollections(): boolean {
    try {
      const validation = ImageValidationService.validateCollectionsIntegrity();

      if (!validation.isValid) {
        console.error('Image collections validation failed:', validation.errors);
        validation.warnings.forEach(warning => console.warn(warning));
        return false;
      }

      if (validation.warnings.length > 0) {
        console.warn('Image collections validation warnings:', validation.warnings);
      }

      console.info('Image collections validated successfully');
      return true;
    } catch (error) {
      console.error('Error validating image collections:', error);
      return false;
    }
  }

  /**
   * Generate health report for monitoring
   * @returns Promise resolving to health report
   */
  static async generateHealthReport() {
    try {
      return await ImageValidationService.generateHealthReport();
    } catch (error) {
      console.error('Error generating health report:', error);
      return null;
    }
  }

  /**
   * Initialize performance optimization services
   */
  static initializePerformanceServices(): void {
    try {
      ImageCacheService.initialize();
      ImagePerformanceService.initialize();

      // Preload critical images
      this.preloadCriticalImages();

      console.info('Image performance services initialized');
    } catch (error) {
      console.error('Error initializing performance services:', error);
    }
  }

  /**
   * Preload critical images based on performance strategy
   */
  private static async preloadCriticalImages(): Promise<void> {
    try {
      // Get all hero and team images (critical for initial page load)
      const heroUrls = Object.values(imageCollections.hero);
      const teamUrls = Object.values(imageCollections.team);
      const criticalUrls = [...heroUrls, ...teamUrls];

      // Preload with high priority
      await ImageCacheService.preloadCriticalImages(criticalUrls);

      console.debug(`Preloaded ${criticalUrls.length} critical images`);
    } catch (error) {
      console.error('Error preloading critical images:', error);
    }
  }

  /**
   * Get optimized image loading strategy
   */
  static getOptimizedLoadingStrategy(): any {
    try {
      // Collect all available image URLs
      const allUrls: string[] = [
        ...Object.values(imageCollections.hero),
        ...Object.values(imageCollections.team)
      ];

      // Add category images (first few from each category)
      Object.keys(imageCollections.categories).forEach(() => {
        // Note: This is async, but we'll handle it in the strategy
        allUrls.push(`category-placeholder`); // Placeholder for strategy calculation
      });

      return ImagePerformanceService.getOptimalLoadingStrategy(allUrls);
    } catch (error) {
      console.error('Error getting loading strategy:', error);
      return { eager: [], lazy: [], background: [], skip: [] };
    }
  }

  /**
   * Preload images for a specific category with performance optimization
   */
  static async preloadCategoryImages(category: string, limit: number = 5): Promise<void> {
    try {
      const categoryLoader = imageCollections.categories[category];
      if (!categoryLoader) {
        console.warn(`Category not found for preloading: ${category}`);
        return;
      }

      const images = await categoryLoader();
      const limitedImages = images.slice(0, limit);

      // Use background preloading for non-critical images
      ImageCacheService.preloadInBackground(limitedImages);

      console.debug(`Started background preload for ${limitedImages.length} images in category: ${category}`);
    } catch (error) {
      console.error(`Error preloading category images for ${category}:`, error);
    }
  }

  /**
   * Get performance metrics and recommendations
   */
  static getPerformanceReport(): {
    metrics: any;
    cacheStats: any;
    recommendations: any;
    summary: any;
  } {
    try {
      return {
        metrics: ImagePerformanceService.getMetrics(),
        cacheStats: ImageCacheService.getCacheStats(),
        recommendations: ImagePerformanceService.generateRecommendations([]),
        summary: ImagePerformanceService.getPerformanceSummary()
      };
    } catch (error) {
      console.error('Error generating performance report:', error);
      return {
        metrics: {},
        cacheStats: {},
        recommendations: {},
        summary: { score: 0, issues: ['Error generating report'], recommendations: [] }
      };
    }
  }

  /**
   * Dispose of performance services
   */
  static disposePerformanceServices(): void {
    try {
      ImageCacheService.dispose();
      ImagePerformanceService.dispose();
      console.info('Image performance services disposed');
    } catch (error) {
      console.error('Error disposing performance services:', error);
    }
  }
}

/**
 * Synchronous versions for backward compatibility
 * These maintain the existing API while the codebase migrates
 *
 * Note: These use the existing FEATURED_IMAGES mapping to provide
 * immediate synchronous access during the migration period.
 */
export class ImageServiceSync {
  /**
   * Synchronous service image getter (for immediate migration)
   * Uses the existing featured images mapping for immediate access
   */
  static getServiceImageSync(serviceId: string): string {
    const category = SERVICE_TO_IMAGE_CATEGORY[serviceId as keyof typeof SERVICE_TO_IMAGE_CATEGORY];
    if (category) {
      // Use the imported FEATURED_IMAGES mapping for sync access
      const filename = FEATURED_IMAGES[category as keyof typeof FEATURED_IMAGES];
      if (filename) {
        const imagePath = `/images/categorized/${category}/${filename}`;
        return encodeImagePath(imagePath);
      }
    }

    console.warn(`Service image not found for: ${serviceId}. Available service IDs:`, Object.keys(SERVICE_TO_IMAGE_CATEGORY));
    return ImageService.getFallbackCategory();
  }

  /**
   * Synchronous project image getter (for immediate migration)
   * Uses the existing featured images mapping for immediate access
   */
  static getProjectImageSync(projectCategory: string): string {
    const category = PROJECT_CATEGORY_TO_IMAGE_CATEGORY[projectCategory as keyof typeof PROJECT_CATEGORY_TO_IMAGE_CATEGORY];
    if (category) {
      // Use the imported FEATURED_IMAGES mapping for sync access
      const filename = FEATURED_IMAGES[category as keyof typeof FEATURED_IMAGES];
      if (filename) {
        const imagePath = `/images/categorized/${category}/${filename}`;
        return encodeImagePath(imagePath);
      }
    }

    console.warn(`Project image not found for: ${projectCategory}. Available categories:`, Object.keys(PROJECT_CATEGORY_TO_IMAGE_CATEGORY));
    return ImageService.getFallbackCategory();
  }

  /**
   * Synchronous team image getter (for immediate migration)
   * Uses the dynamic image collections for immediate access
   */
  static getTeamImage(memberId: string): string {
    const image = imageCollections.team[memberId];
    if (image) {
      return encodeImagePath(image);
    }

    console.warn(`Team image not found: ${memberId}. Available keys:`, Object.keys(imageCollections.team));
    return ImageService.getFallbackTeam();
  }

  /**
   * Synchronous icon image getter (for immediate migration)
   * Uses the icon mapping for immediate access
   */
  static getIconImage(key: string): string {
    return ImageService.getIconImage(key);
  }
}

export default ImageService;
