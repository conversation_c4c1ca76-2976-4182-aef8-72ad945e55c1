import React, { useState, useEffect } from 'react';
import { X } from 'lucide-react';
import { cn } from '@/lib/utils';
import { IMAGE_CATEGORIES, GeoImage } from '@/lib/utils/images';
import { encodeImagePath } from '@/lib/utils/paths';
import { ImageService } from '@/lib/services/ImageService';

interface ProjectGalleryProps {
  category?: keyof typeof IMAGE_CATEGORIES;
  className?: string;
}

const ProjectGallery: React.FC<ProjectGalleryProps> = ({
  category,
  className
}) => {
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [images, setImages] = useState<GeoImage[]>([]);
  const [loading, setLoading] = useState(true);

  // Load images dynamically when category changes
  useEffect(() => {
    const loadImages = async () => {
      if (!category) {
        setImages([]);
        setLoading(false);
        return;
      }

      setLoading(true);
      try {
        const galleryImages = await ImageService.getCategoryGalleryImages(category);
        setImages(galleryImages);
      } catch (error) {
        console.error('Error loading gallery images:', error);
        setImages([]);
      } finally {
        setLoading(false);
      }
    };

    loadImages();
  }, [category]);

  if (loading) {
    return (
      <div className="text-center py-8">
        <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
        <p className="text-gray-500 mt-2">Laster bilder...</p>
      </div>
    );
  }

  if (!images.length) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">Ingen bilder tilgjengelig for denne kategorien.</p>
      </div>
    );
  }

  return (
    <>
      <div className={cn(
        "grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",
        className
      )}>
        {images.map((image, index) => (
          <button
            key={index}
            className="relative aspect-square overflow-hidden rounded-lg cursor-pointer group"
            onClick={() => setSelectedImage(image.path)}
          >
            <img
              src={encodeImagePath(image.path)}
              alt={image.metadata?.title || ''}
              className="absolute inset-0 w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
            />
            <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <div className="absolute bottom-0 left-0 right-0 p-4 text-white">
                <h3 className="text-sm font-medium">
                  {image.metadata?.title}
                </h3>
                {image.metadata?.description && (
                  <p className="text-xs mt-1 text-gray-200">
                    {image.metadata.description}
                  </p>
                )}
              </div>
            </div>
          </button>
        ))}
      </div>

      {/* Modal */}
      {selectedImage && (
        <div
          className="fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4"
          onClick={() => setSelectedImage(null)}
        >
          <button
            className="absolute top-4 right-4 text-white p-2 hover:bg-white/10 rounded-full"
            onClick={() => setSelectedImage(null)}
          >
            <X className="w-6 h-6" />
          </button>
          <img
            src={encodeImagePath(selectedImage)}
            alt="Project detail"
            className="max-w-full max-h-[90vh] rounded-lg"
          />
        </div>
      )}
    </>
  );
};

export default ProjectGallery;